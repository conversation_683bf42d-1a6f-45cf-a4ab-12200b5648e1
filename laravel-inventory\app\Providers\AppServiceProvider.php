<?php

namespace App\Providers;

use App\Http\Middleware\AdminMiddleware;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Validator;
use Illuminate\Routing\Router;
use Laravel\Passport\Passport;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(Router $router)
    {
        // Add custom validation rule
        Validator::extend('email_domain', 'App\Rules\EmailDomain@passes');

        // Register the AdminMiddleware
        $router->aliasMiddleware('admin', AdminMiddleware::class);

        // Configure Passport token expiration
        Passport::tokensExpireIn(now()->addDays(15));
        Passport::refreshTokensExpireIn(now()->addDays(30));
        Passport::personalAccessTokensExpireIn(now()->addMonths(6));

        // Configure Passport to ignore CSRF for API routes
        Passport::ignoreCsrfToken();

        // Set default scopes
        Passport::tokensCan([
            'read' => 'Read access',
            'write' => 'Write access',
        ]);

        Passport::setDefaultScope(['read']);
    }
}
